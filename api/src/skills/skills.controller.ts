import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  UseGuards,
  Query,
} from '@nestjs/common';
import { SkillsService } from './skills.service';
import { RoleGuard } from '@/guards/role.guard';
import {
  ApiBearerAuth,
  ApiResponse,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiQuery,
} from '@nestjs/swagger';
import {
  SkillDto,
  SkillParam,
  SkillCategoryDto,
  SkillCategoryParam,
  SkillQueryParamsDto,
  AddStudentSkillsDto,
} from './skills.dto';
import { UseRoles } from 'nest-access-control';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { AppClients } from '@app/shared/constants/auth.constants';
import { CustomParseUUIDPipe } from '@/common/pipes/custom-parse-uuid';
import { SkillRoutes } from '@app/shared/constants/skills.constants';
import { User } from '@/guards/user.decorator';
import { type User as UserDecoratorType } from '@/db/schema';

@Controller({ version: '1', path: 'skills' })
@ApiTags('Skills')
export class SkillsController {
  private readonly logger = new Logger(SkillsController.name);

  constructor(private readonly skillsService: SkillsService) {}

  @Post(SkillRoutes.ADD_SKILL)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill', action: 'create', possession: 'any' })
  @ApiOperation({
    summary: 'Create a new skill',
    description: 'Creates a new skill in the system.',
  })
  @ApiBody({
    type: SkillDto,
    description: 'Skill data to create',
  })
  @ApiResponse({
    status: 201,
    description: 'The skill has been successfully created.',
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  @ApiBearerAuth()
  async addSkill(@Body() skillDto: SkillDto): Promise<SkillParam> {
    try {
      return await this.skillsService.addSkill(skillDto);
    } catch (error: any) {
      this.logger.error('Error adding skill', error.stack);
      throw error;
    }
  }

  @Post(SkillRoutes.ADD_STUDENT_SKILL)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'student_skill', action: 'create', possession: 'own' })
  @ApiOperation({
    summary: 'Add skills to student profile',
    description:
      "Adds one or more skills to the authenticated student's profile.",
  })
  @ApiBody({
    type: AddStudentSkillsDto,
    description: 'Skills to add to student profile',
  })
  @ApiOkResponse({
    description: 'Skills successfully added to student profile',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data or user has no student profile',
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.MOBILE)
  async addStudentSkill(
    @Body() studentSkillDto: AddStudentSkillsDto,
    @User() user: UserDecoratorType,
  ) {
    try {
      if (!user.student_profile) {
        this.logger.error('User does not have a student profile');
        throw new BadRequestException('User does not have a student profile');
      }
      this.logger.log(
        `Adding skills for student with id ${user.student_profile.id}`,
      );

      return await this.skillsService.addStudentSkill({
        studentId: user.student_profile.id,
        skills: studentSkillDto.skills,
      });
    } catch (error: any) {
      this.logger.error(
        `Error adding skills for student with id user id ${user.id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(SkillRoutes.DELETE_STUDENT_SKILL)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'student_skill', action: 'delete', possession: 'own' })
  @ApiOperation({
    summary: 'Remove skill from student profile',
    description:
      "Removes a specific skill from the authenticated student's profile.",
  })
  @ApiParam({
    name: 'skillId',
    description: 'ID of the skill to remove',
    type: String,
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Skill successfully removed from student profile',
  })
  @ApiBadRequestResponse({
    description: 'Invalid skill ID or user has no student profile',
  })
  @ApiNotFoundResponse({ description: 'Skill not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.MOBILE)
  async deleteStudentSkill(
    @Param('skillId', new CustomParseUUIDPipe()) skillId: string,
    @User() user: UserDecoratorType,
  ) {
    if (!user.student_profile) {
      this.logger.error('User does not have a student profile');
      throw new BadRequestException('User does not have a student profile');
    }
    this.logger.log(
      `Deleting skill with id ${skillId} for student with id ${user.student_profile.id}`,
    );
    try {
      if (
        user.student_profile &&
        user.student_profile.id !== user.student_profile.id
      ) {
        this.logger.log(
          `User is requesting to delete skill for student with id ${user.student_profile.id}`,
        );
        throw new BadRequestException(
          'Students cannot delete skills for other students',
        );
      }
      return await this.skillsService.removeStudentSkill(
        user.student_profile.id,
        skillId,
      );
    } catch (error: any) {
      this.logger.error(
        `Error deleting skill with id ${skillId} for student with id ${user.student_profile.id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(SkillRoutes.GET_ALL_SKILLS)
  @ApiOperation({
    summary: 'Get all skills',
    description:
      'Retrieves a list of all skills with optional filtering and pagination.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term for skill names',
    type: String,
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    description: 'Field to sort by',
    type: String,
  })
  @ApiQuery({
    name: 'order',
    required: false,
    description: 'Sort order (asc or desc)',
    enum: ['asc', 'desc'],
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'The skills have been successfully fetched.',
  })
  @ApiBadRequestResponse({ description: 'Invalid query parameters' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill', action: 'read', possession: 'any' })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getAllSkills(
    @Query() query: SkillQueryParamsDto,
  ): Promise<SkillParam[]> {
    this.logger.log('Fetching all skills');
    try {
      return await this.skillsService.getAllSkills(
        query as SkillQueryParamsDto & {
          sort: keyof SkillParam;
        },
      );
    } catch (error: any) {
      this.logger.error('Error fetching all skills', error.stack);
      throw error;
    }
  }

  @Get(SkillRoutes.GET_SKILLS_BY_CATEGORY)
  @ApiOperation({
    summary: 'Get skills by category',
    description: 'Retrieves all skills belonging to a specific category.',
  })
  @ApiParam({
    name: 'categoryId',
    description: 'ID of the skill category',
    type: String,
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'The skills by category have been successfully fetched.',
  })
  @ApiBadRequestResponse({ description: 'Invalid category ID' })
  @ApiNotFoundResponse({ description: 'Category not found' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getSkillsByCategory(
    @Param('categoryId', new CustomParseUUIDPipe()) categoryId: string,
  ): Promise<SkillParam[]> {
    this.logger.log(`Fetching skills for category ${categoryId}`);
    try {
      return await this.skillsService.getSkillsByCategory(categoryId);
    } catch (error: any) {
      this.logger.error(
        `Error fetching skills for category ${categoryId}`,
        error.stack,
      );
      throw error;
    }
  }

  @Put(SkillRoutes.UPDATE_SKILL)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill', action: 'update', possession: 'any' })
  @ApiOperation({
    summary: 'Update a skill',
    description: 'Updates an existing skill with new information.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the skill to update',
    type: String,
    format: 'uuid',
  })
  @ApiBody({
    type: SkillDto,
    description: 'Updated skill data',
  })
  @ApiOkResponse({
    description: 'Skill successfully updated',
  })
  @ApiBadRequestResponse({ description: 'Invalid input data or skill ID' })
  @ApiNotFoundResponse({ description: 'Skill not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  async updateSkill(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() skillDto: SkillDto,
  ): Promise<SkillParam> {
    this.logger.log(`Updating skill with id ${id}`);

    try {
      return await this.skillsService.updateSkill(id, skillDto);
    } catch (error: any) {
      this.logger.error(`Error updating skill with id ${id}`, error.stack);
      throw error;
    }
  }

  @Delete(SkillRoutes.DELETE_SKILL)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill', action: 'delete', possession: 'own' })
  @ApiOperation({
    summary: 'Delete a skill',
    description: 'Deletes an existing skill from the system.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the skill to delete',
    type: String,
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Skill successfully deleted',
  })
  @ApiBadRequestResponse({ description: 'Invalid skill ID' })
  @ApiNotFoundResponse({ description: 'Skill not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  async deleteSkill(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @User() user: UserDecoratorType,
  ) {
    this.logger.log(`Deleting skill with id ${id}`);

    try {
      return await this.skillsService.deleteSkill(id, user);
    } catch (error: any) {
      this.logger.error(`Error deleting skill with id ${id}`, error.stack);
      throw error;
    }
  }

  // Skill Categories endpoints
  @Post(SkillRoutes.ADD_SKILL_CATEGORY)
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill_category', action: 'create', possession: 'any' })
  @ApiOperation({
    summary: 'Create a new skill category',
    description: 'Creates a new skill category in the system.',
  })
  @ApiBody({
    type: SkillCategoryDto,
    description: 'Skill category data to create',
  })
  @ApiResponse({
    status: 201,
    description: 'The skill category has been successfully created.',
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  @ApiBearerAuth()
  async addSkillCategory(
    @Body() skillCategoryDto: SkillCategoryDto,
  ): Promise<SkillCategoryParam> {
    try {
      return await this.skillsService.addSkillCategory(skillCategoryDto);
    } catch (error: any) {
      this.logger.error('Error adding skill category', error.stack);
      throw error;
    }
  }

  @Get(SkillRoutes.GET_SKILL_CATEGORY_BY_ID)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @ApiOperation({
    summary: 'Get skill category by ID',
    description: 'Retrieves a specific skill category by its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the skill category',
    type: String,
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Skill category successfully retrieved',
  })
  @ApiBadRequestResponse({ description: 'Invalid category ID' })
  @ApiNotFoundResponse({ description: 'Skill category not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getSkillCategory(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<SkillCategoryParam> {
    this.logger.log(`Fetching skill category with id ${id}`);

    try {
      return await this.skillsService.getSkillCategoryById(id);
    } catch (error: any) {
      this.logger.error(
        `Error fetching skill category with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(SkillRoutes.GET_ALL_SKILL_CATEGORIES)
  @ApiOperation({
    summary: 'Get all skill categories',
    description: 'Retrieves a list of all skill categories.',
  })
  @ApiResponse({
    status: 200,
    description: 'The skill categories have been successfully fetched.',
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiBearerAuth()
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getAllSkillCategories(): Promise<SkillCategoryParam[]> {
    this.logger.log('Fetching all skill categories');
    try {
      return await this.skillsService.getAllSkillCategories();
    } catch (error: any) {
      this.logger.error('Error fetching all skill categories', error.stack);
      throw error;
    }
  }

  @Put(SkillRoutes.UPDATE_SKILL_CATEGORY)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill_category', action: 'update', possession: 'any' })
  @ApiOperation({
    summary: 'Update a skill category',
    description: 'Updates an existing skill category with new information.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the skill category to update',
    type: String,
    format: 'uuid',
  })
  @ApiBody({
    type: SkillCategoryDto,
    description: 'Updated skill category data',
  })
  @ApiOkResponse({
    description: 'Skill category successfully updated',
  })
  @ApiBadRequestResponse({ description: 'Invalid input data or category ID' })
  @ApiNotFoundResponse({ description: 'Skill category not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  async updateSkillCategory(
    @Param('id', new CustomParseUUIDPipe()) id: string,
    @Body() skillCategoryDto: SkillCategoryDto,
  ): Promise<SkillCategoryParam> {
    this.logger.log(`Updating skill category with id ${id}`);

    try {
      return await this.skillsService.updateSkillCategory(id, skillCategoryDto);
    } catch (error: any) {
      this.logger.error(
        `Error updating skill category with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Delete(SkillRoutes.DELETE_SKILL_CATEGORY)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @UseRoles({ resource: 'skill_category', action: 'delete', possession: 'any' })
  @ApiOperation({
    summary: 'Delete a skill category',
    description: 'Deletes an existing skill category from the system.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the skill category to delete',
    type: String,
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Skill category successfully deleted',
  })
  @ApiBadRequestResponse({ description: 'Invalid category ID' })
  @ApiNotFoundResponse({ description: 'Skill category not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @CLIENT_TYPE(AppClients.WEB)
  async deleteSkillCategory(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ) {
    this.logger.log(`Deleting skill category with id ${id}`);

    try {
      return await this.skillsService.deleteSkillCategory(id);
    } catch (error: any) {
      this.logger.error(
        `Error deleting skill category with id ${id}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(SkillRoutes.GET_SKILL_BY_ID)
  @ApiBearerAuth()
  @UseGuards(RoleGuard)
  @ApiOperation({
    summary: 'Get skill by ID',
    description: 'Retrieves a specific skill by its ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the skill',
    type: String,
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Skill successfully retrieved',
  })
  @ApiBadRequestResponse({ description: 'Invalid skill ID' })
  @ApiNotFoundResponse({ description: 'Skill not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @CLIENT_TYPE(AppClients.MOBILE, AppClients.WEB)
  async getSkill(
    @Param('id', new CustomParseUUIDPipe()) id: string,
  ): Promise<SkillParam> {
    this.logger.log(`Fetching skill with id ${id}`);

    try {
      return await this.skillsService.getSkillById(id);
    } catch (error: any) {
      this.logger.error(`Error fetching skill with id ${id}`, error.stack);
      throw error;
    }
  }
}
