import { Test, TestingModule } from '@nestjs/testing';
import { EventService } from './event.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { PostRepository } from '@/repositories/post.repository';
import { PostService } from '../post.service';
import { CacheService } from '@app/shared/redis/cache.service';
import { NotFoundException } from '@nestjs/common';

describe('EventService', () => {
  let service: EventService;

  const mockDrizzleService = {
    db: {
      query: {
        events: {
          findFirst: jest.fn(),
        },
        posts: {
          findFirst: jest.fn(),
        },
      },
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      then: jest.fn().mockResolvedValue([]),
      transaction: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      returning: jest.fn().mockResolvedValue([]),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    },
  };

  const mockCacheService = {
    get: jest.fn().mockResolvedValue(null), // Always return null to bypass cache
    set: jest.fn().mockResolvedValue(undefined),
    del: jest.fn().mockResolvedValue(undefined),
    invalidatePattern: jest.fn().mockResolvedValue(undefined),
    invalidateMany: jest.fn().mockResolvedValue(undefined),
    generateKey: jest
      .fn()
      .mockImplementation(
        (keys, prefix) =>
          `${prefix}:${Array.isArray(keys) ? keys.join(':') : keys}`,
      ),
    generateResourceKey: jest
      .fn()
      .mockImplementation((entityId, prefix) => `${prefix}:${entityId}`),
  };

  const mockPostRepository = {
    createPost: jest.fn(),
    updatePost: jest.fn(),
    updateEvent: jest.fn(),
  };

  const mockPostService = {
    uploadPostAttachments: jest.fn(),
    sendPostNotifications: jest.fn(),
    getPostById: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EventService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: PostRepository,
          useValue: mockPostRepository,
        },
        {
          provide: PostService,
          useValue: mockPostService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<EventService>(EventService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getEventById', () => {
    const eventId = '123e4567-e89b-12d3-a456-426614174000';
    const mockEvent = {
      id: eventId,
      startDate: '2024-01-01',
      endDate: '2024-01-02',
      startTime: '10:00:00',
      endTime: '12:00:00',
      virtualLink: 'https://example.com',
      postId: 'post-123',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      post: {
        id: 'post-123',
        title: 'Test Event',
        description: 'Test event description',
        status: 'active',
        type: 'event',
        postedBy: {
          id: 'user-123',
          profile: {
            id: 'profile-123',
            email: '<EMAIL>',
            name: 'Test User',
          },
          student_profile: {
            id: 'student-123',
            first_name: 'Test',
            last_name: 'User',
          },
        },
        countries: [],
        institutions: [],
        postEngagements: [],
        images: [],
        club: null,
      },
    };

    it('should return an event when found', async () => {
      mockDrizzleService.db.query.events.findFirst.mockResolvedValue(mockEvent);

      const result = await service.getEventById(eventId);

      expect(result).toEqual(mockEvent);
      expect(mockDrizzleService.db.query.events.findFirst).toHaveBeenCalledWith(
        {
          where: expect.anything(),
          with: expect.objectContaining({
            post: expect.any(Object),
          }),
        },
      );
    });

    it('should throw NotFoundException when event not found', async () => {
      mockDrizzleService.db.query.events.findFirst.mockResolvedValue(null);

      await expect(service.getEventById(eventId)).rejects.toThrow(
        new NotFoundException(`Event with ID ${eventId} not found`),
      );
      expect(mockDrizzleService.db.query.events.findFirst).toHaveBeenCalledWith(
        {
          where: expect.anything(),
          with: expect.objectContaining({
            post: expect.any(Object),
          }),
        },
      );
    });

    it('should handle invalid UUID format', async () => {
      const invalidId = 'invalid-uuid';
      mockDrizzleService.db.query.events.findFirst.mockResolvedValue(null);

      await expect(service.getEventById(invalidId)).rejects.toThrow(
        new NotFoundException(`Event with ID ${invalidId} not found`),
      );
      expect(mockDrizzleService.db.query.events.findFirst).toHaveBeenCalledWith(
        {
          where: expect.anything(),
          with: expect.objectContaining({
            post: expect.any(Object),
          }),
        },
      );
    });

    it('should have getEventById method defined', () => {
      expect(service.getEventById).toBeDefined();
      expect(typeof service.getEventById).toBe('function');
    });
  });

  describe('createEvent', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      role: 'admin',
    };

    const mockEventData = {
      title: 'Test Event',
      description: 'Test event description',
      startDate: '2024-01-01',
      endDate: '2024-01-02',
      startTime: '10:00:00',
      endTime: '12:00:00',
      virtualLink: 'https://example.com',
      isGlobal: true,
      notify_users: false,
      countries: [],
      institutions: [],
      status: 'active' as const,
      scheduledAt: null,
    };

    const mockCreatedPost = {
      id: 'post-123',
      title: 'Test Event',
      description: 'Test event description',
      status: 'active',
      type: 'event',
      postedBy: 'user-123',
      isGlobal: true,
      notify_users: false,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
    };

    const mockCreatedEvent = {
      id: 'event-123',
      postId: 'post-123',
      startDate: '2024-01-01',
      endDate: '2024-01-02',
      startTime: '10:00:00',
      endTime: '12:00:00',
      virtualLink: 'https://example.com',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
    };

    const mockCompleteEvent = {
      id: 'post-123',
      title: 'Test Event',
      description: 'Test event description',
      imageUrl: null,
      status: 'active',
      disabled: false,
      type: 'event',
      postedBy: {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'admin',
        state: 'active',
        profile_pic_url: null,
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
        deleted: false,
        deleted_at: null,
        profile: {
          id: 'profile-123',
          email: '<EMAIL>',
          name: 'Test User',
        },
        student_profile: null,
      },
      isGlobal: true,
      notify_users: false,
      club_id: null,
      scheduled_at: null,
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
      countries: [],
      institutions: [],
      opportunity: null,
      event: mockCreatedEvent,
      postEngagements: [],
      images: [],
      club: null,
    };

    beforeEach(() => {
      // Reset all mocks before each test
      jest.clearAllMocks();

      // Setup default mock implementations
      mockPostRepository.createPost.mockResolvedValue([mockCreatedPost]);
      mockDrizzleService.db.transaction.mockImplementation(async (callback) => {
        const mockTx = {
          insert: jest.fn().mockReturnThis(),
          values: jest.fn().mockReturnThis(),
          returning: jest.fn().mockResolvedValue([mockCreatedEvent]),
        };
        return callback(mockTx);
      });
      // Mock the PostService.getPostById method instead of direct DB query
      mockPostService.getPostById.mockResolvedValue(mockCompleteEvent);
    });

    it('should create an event and return complete event data', async () => {
      const result = await service.createEvent(
        mockEventData,
        mockUser as any,
        [],
      );

      expect(result).toEqual(mockCompleteEvent);
      expect(mockPostRepository.createPost).toHaveBeenCalledWith(
        expect.objectContaining({
          title: mockEventData.title,
          description: mockEventData.description,
          postedBy: mockUser.id,
          type: 'event',
          isGlobal: mockEventData.isGlobal,
          status: 'active',
          notify_users: mockEventData.notify_users,
        }),
        expect.any(Object),
      );
      // Check that PostService.getPostById was called instead of direct DB query
      expect(mockPostService.getPostById).toHaveBeenCalledWith(
        mockCreatedPost.id,
        mockUser,
      );
    });

    it('should create event with attachments and set status to pending', async () => {
      const mockAttachments = [
        { originalname: 'test.jpg', buffer: Buffer.from('test') },
      ] as Express.Multer.File[];

      const eventDataWithAttachments = {
        ...mockEventData,
        status: 'active' as const,
      };

      // Mock the post creation to return pending status when attachments are present
      const mockCreatedPostWithAttachments = {
        ...mockCreatedPost,
        status: 'pending',
      };
      mockPostRepository.createPost.mockResolvedValue([
        mockCreatedPostWithAttachments,
      ]);

      const result = await service.createEvent(
        eventDataWithAttachments,
        mockUser as any,
        mockAttachments,
      );

      expect(mockPostRepository.createPost).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'pending', // Should be pending when attachments are present
        }),
        expect.any(Object),
      );
      expect(mockPostService.uploadPostAttachments).toHaveBeenCalledWith(
        mockCreatedPost.id,
        mockAttachments,
        'active',
      );
      expect(mockPostService.getPostById).toHaveBeenCalledWith(
        mockCreatedPostWithAttachments.id,
        mockUser,
      );
    });

    it('should handle empty countries and institutions arrays', async () => {
      const eventDataWithEmptyArrays = {
        ...mockEventData,
        countries: [],
        institutions: [],
      };

      const result = await service.createEvent(
        eventDataWithEmptyArrays,
        mockUser as any,
        [],
      );

      // Should not call validation methods for empty arrays
      expect(result).toEqual(mockCompleteEvent);
      expect(mockPostRepository.createPost).toHaveBeenCalled();
      expect(mockPostService.getPostById).toHaveBeenCalledWith(
        mockCreatedPost.id,
        mockUser,
      );
    });

    it('should send notifications when notify_users is true', async () => {
      const eventDataWithNotification = {
        ...mockEventData,
        notify_users: true,
      };

      await service.createEvent(eventDataWithNotification, mockUser as any, []);

      expect(mockPostService.sendPostNotifications).toHaveBeenCalledWith(
        mockCreatedPost,
        eventDataWithNotification,
      );
      expect(mockPostService.getPostById).toHaveBeenCalledWith(
        mockCreatedPost.id,
        mockUser,
      );
    });

    it('should handle notification errors gracefully', async () => {
      const eventDataWithNotification = {
        ...mockEventData,
        notify_users: true,
      };

      mockPostService.sendPostNotifications.mockRejectedValue(
        new Error('Notification service unavailable'),
      );

      // Should not throw error even if notification fails
      const result = await service.createEvent(
        eventDataWithNotification,
        mockUser as any,
        [],
      );

      expect(result).toEqual(mockCompleteEvent);
      expect(mockPostService.sendPostNotifications).toHaveBeenCalled();
      expect(mockPostService.getPostById).toHaveBeenCalledWith(
        mockCreatedPost.id,
        mockUser,
      );
    });

    it('should cache the created event', async () => {
      await service.createEvent(mockEventData, mockUser as any, []);

      expect(mockCacheService.set).toHaveBeenCalledWith(
        expect.stringContaining('event:'),
        mockCompleteEvent,
        expect.any(Number),
      );
      expect(mockPostService.getPostById).toHaveBeenCalledWith(
        mockCreatedPost.id,
        mockUser,
      );
    });

    it('should throw error if post creation fails', async () => {
      mockPostRepository.createPost.mockResolvedValue([null]);

      await expect(
        service.createEvent(mockEventData, mockUser as any, []),
      ).rejects.toThrow('Failed to create post');
    });

    it('should throw error if complete event data fetch fails', async () => {
      // Mock PostService.getPostById to throw NotFoundException
      mockPostService.getPostById.mockRejectedValue(
        new NotFoundException('Post not found'),
      );

      await expect(
        service.createEvent(mockEventData, mockUser as any, []),
      ).rejects.toThrow('Post not found');

      expect(mockPostService.getPostById).toHaveBeenCalledWith(
        mockCreatedPost.id,
        mockUser,
      );
    });
  });
});
