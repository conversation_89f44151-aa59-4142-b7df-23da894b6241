import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { student_profiles } from 'src/db/schema/student_profile';
import {
  and,
  count,
  eq,
  getTableColumns,
  inArray,
  ilike,
  SQL,
  sql,
  asc,
  desc,
  or,
  not,
} from 'drizzle-orm';
import {
  StudentProfileParams,
  StudentProfileQueryParamsDto,
} from './student_profile.dto';
import {
  countries,
  institutions,
  User,
  user_states,
  users,
  student_clubs,
  student_club_memberships,
  user_roles,
} from '@/db/schema';
import { PointConstant } from '@app/shared/constants/points-system.constant';
import { PointSystemRepository } from '@/point-system/repository/point_system.repository';
import { type User as UserDecoratorType } from '@/db/schema';
import { LeaderBoardService } from '@/mcq/leader-board/leader-board.service';
import { PeriodEnumType } from '@/mcq/leader-board/leader-board.types';

@Injectable()
export class StudentProfileService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly pointSystemRepository: PointSystemRepository,
    private readonly leaderboardService: LeaderBoardService,
  ) {}
  private readonly logger = new Logger(StudentProfileService.name);

  /**
   * Adds a new student profile to the database.
   *
   * @param studentProfileInput - The student profile data to add.
   * @param id - The ID of the user associated with the student profile.
   * @returns The created student profile.
   * @throws Error if the student profile already exists.
   */
  async addStudentProfile(
    studentProfileInput: StudentProfileParams,
    user: UserDecoratorType,
  ) {
    const {
      first_name,
      last_name,
      other_name,
      country_id,
      date_of_birth,
      phone_number,
      institution_id,
      enrollment_date,
      graduation_date,
      github_profile,
      linkedin_profile,
      club_id,
      degree,
      programme,
      about,
      username,
    } = studentProfileInput;

    if (username) {
      const usernameExists = await this.drizzle.db.query.student_profiles
        .findFirst({
          where: eq(student_profiles.username, username),
        })
        .execute();
      if (usernameExists) {
        this.logger.warn(`Username ${username} already exists`);
        throw new ConflictException('Username already in use');
      }
    } else {
      studentProfileInput.username = this.generateUserHandle(
        first_name,
        last_name,
      );
    }
    if (user.student_profile) {
      this.logger.warn(
        `Student with ID ${user?.student_profile?.id} already exists`,
      );
      throw new ConflictException('Student already exists');
    }
    const institution = await this.drizzle.db.query.institutions
      .findFirst({
        where: eq(institutions.id, institution_id),
      })
      .execute();
    if (!institution || institution.disabled) {
      this.logger.warn(`Institution with ID ${institution_id} not found`);
      throw new BadRequestException('Institution not found');
    }
    const domain = institution.domain;
    if (domain && !user.email.endsWith(domain)) {
      this.logger.warn(
        `User email ${user.email} does not match institution domain ${domain}`,
      );
      throw new BadRequestException(
        `User email does not match institution domain ${domain}`,
      );
    }

    const [profile] = await this.drizzle.db
      .insert(student_profiles)
      .values({
        user_id: user.id,
        country_id,
        date_of_birth,
        phone_number,
        institution_id,
        enrollment_date,
        graduation_date,
        github_profile,
        linkedin_profile,
        club_id,
        first_name,
        last_name,
        other_name,
        degree,
        programme,
        about,
      })
      .returning();

    await this.drizzle.db
      .update(users)
      .set({ state: user_states.ACTIVE })
      .where(eq(users.id, user.id));

    // Award points for completing the profile
    if (this.isProfileValid(profile)) {
      await this.pointSystemRepository.awardPointsToStudent(
        PointConstant.MODULES.PROFILE,
        PointConstant.ACTIONS.COMPLETE_PROFILE,
        (profile as { id: string }).id,
      );
    }

    return this.getCompleteProfile(profile!.id);
  }

  /**
   * Retrieves a student profile by ID from the database.
   *
   * @param studentProfileId - The ID of the student profile to retrieve.
   * @returns The student profile.
   * @throws BadRequestException if the student profile is not found.
   */
  async getStudentProfileById(
    studentProfileId: string,
    user?: UserDecoratorType,
  ) {
    const profile = await this.getCompleteProfile(studentProfileId, user);
    if (!profile) {
      throw new BadRequestException('Student profile not found');
    }
    return profile;
  }

  async getStudentProfileByUsername(username: string, user: UserDecoratorType) {
    const filters: Array<SQL | undefined> = [
      eq(student_profiles.username, username),
    ];
    if (user.role !== user_roles.SUPER_ADMIN) {
      filters.push(
        and(eq(users.deleted, false), eq(users.state, user_states.ACTIVE)),
      );
    }

    const [profile] = await this.drizzle.db
      .select({
        id: student_profiles.id,
      })
      .from(student_profiles)
      .leftJoin(users, and(eq(users.id, student_profiles.user_id)))
      .where(and(...filters));

    if (!profile) {
      throw new BadRequestException('Student profile not found');
    }
    const completeProfile = await this.getCompleteProfile(profile.id, user);
    if (!completeProfile) {
      throw new BadRequestException('Student profile not found');
    }

    return completeProfile;
  }

  /**
   * Retrieves a single student profile from the database.
   *
   * @param user - The user associated with the student profile.
   * @returns The student profile.
   * @throws BadRequestException if the student profile is not found.
   */
  async getSingleStudentProfile(user: User) {
    const [studentProfile] = await this.drizzle.db
      .select({
        ...getTableColumns(student_profiles),
        user: {
          ...getTableColumns(users),
        },
      })
      .from(student_profiles)
      .leftJoin(
        users,
        and(eq(users.id, student_profiles.user_id), eq(users.deleted, false)),
      )
      .where(eq(student_profiles.user_id, user.id));
    if (!studentProfile) {
      throw new BadRequestException('Student profile not found');
    }

    // Daily logins
    await this.pointSystemRepository.awardPointsToStudent(
      PointConstant.MODULES.USER,
      PointConstant.ACTIONS.LOGIN,
      studentProfile.user_id,
    );
    return studentProfile;
  }

  /**
   * Updates a student profile in the database.
   *
   * @param studentProfileId - The ID of the student profile to update.
   * @param studentProfileInput - The new student profile data.
   * @param user_id - The ID of the user associated with the student profile.
   * @returns The updated student profile.
   * @throws BadRequestException if the student profile is not found.
   */
  async updateStudentProfile(
    studentProfileId: string,
    studentProfileInput: StudentProfileParams,
    user_id: string,
  ) {
    const {
      first_name,
      last_name,
      other_name,
      country_id,
      date_of_birth,
      phone_number,
      institution_id,
      enrollment_date,
      graduation_date,
      github_profile,
      linkedin_profile,
      club_id,
      profile_pic_url,
      degree,
      programme,
      about,
      username,
    } = studentProfileInput;

    // Update profile picture if provided
    if (profile_pic_url) {
      await this.drizzle.db
        .update(users)
        .set({ profile_pic_url })
        .where(eq(users.id, user_id));
    }

    if (username) {
      const existingProfile = await this.drizzle.db.query.student_profiles
        .findFirst({
          where: eq(student_profiles.id, studentProfileId),
        })
        .execute();
      if (!existingProfile)
        throw new BadRequestException('Student profile not found');

      const lastUsernameUpdate = existingProfile.username_last_updated;
      if (this.checkIfUsernameHasChanged(lastUsernameUpdate)) {
        throw new ConflictException(
          'Username can only be changed once every 3 months',
        );
      }

      // Check if username already exists for another user
      const usernameExists = await this.drizzle.db.query.student_profiles
        .findFirst({
          where: and(
            eq(student_profiles.username, username),
            not(eq(student_profiles.id, studentProfileId)),
          ),
        })
        .execute();

      if (usernameExists) {
        this.logger.warn(`Username ${username} already exists`);
        throw new ConflictException('Username already in use');
      }
    }

    // Update the student profile
    const [updatedProfile] = await this.drizzle.db
      .update(student_profiles)
      .set({
        country_id,
        date_of_birth,
        phone_number,
        institution_id,
        enrollment_date,
        graduation_date,
        github_profile,
        linkedin_profile,
        club_id,
        first_name,
        last_name,
        other_name,
        programme,
        degree,
        about,
        ...(username && {
          username,
          username_last_updated: new Date().toISOString(),
        }),
      })
      .where(
        and(
          eq(student_profiles.id, studentProfileId),
          eq(student_profiles.user_id, user_id),
        ),
      )
      .returning();

    if (!updatedProfile) {
      throw new BadRequestException('Student profile not found');
    }

    // Award points for completing the profile
    if (this.isProfileValid(updatedProfile)) {
      await this.pointSystemRepository.awardPointsToStudent(
        PointConstant.MODULES.PROFILE,
        PointConstant.ACTIONS.COMPLETE_PROFILE,
        updatedProfile.user_id,
      );
    }

    return this.getCompleteProfile(studentProfileId);
  }

  /**
   * Retrieves the complete profile with all joined data.
   *
   * @param studentProfileId - The ID of the student profile to retrieve.
   * @returns The complete student profile with all joined data.
   */
  private async getCompleteProfile(
    studentProfileId: string,
    user?: UserDecoratorType,
  ) {
    const filters: Array<SQL | undefined> = [
      eq(student_profiles.id, studentProfileId),
    ];

    if (user?.role !== user_roles.SUPER_ADMIN) {
      filters.push(and(eq(users.deleted, false)));
    }

    const [profile] = await this.drizzle.db
      .select({
        ...getTableColumns(student_profiles),
        email: users.email,
        profile_pic_url: users.profile_pic_url,
        institution_name: institutions.name,
        country_name: countries.name,
        club_name: student_clubs.name,
        club_id: student_club_memberships.club_id,
      })
      .from(student_profiles)
      .leftJoin(users, and(eq(users.id, student_profiles.user_id)))
      .leftJoin(
        institutions,
        eq(institutions.id, student_profiles.institution_id),
      )
      .leftJoin(countries, eq(countries.id, student_profiles.country_id))
      .leftJoin(
        student_club_memberships,
        eq(student_club_memberships.student_id, student_profiles.id),
      )
      .leftJoin(
        student_clubs,
        eq(student_clubs.id, student_club_memberships.club_id),
      )
      .where(and(...filters));

    const [studentPoints] = await this.leaderboardService.getStudentRank(
      studentProfileId,
      PeriodEnumType.ALL_TIME,
    );

    if (!profile) {
      throw new BadRequestException('Student profile not found');
    }

    await this.pointSystemRepository.awardPointsToStudent(
      PointConstant.MODULES.USER,
      PointConstant.ACTIONS.LOGIN,
      studentProfileId,
    );

    return {
      ...profile,
      total_points: parseFloat(studentPoints?.total_score) ?? 0,
      leaderboard_rank: parseInt(studentPoints?.rank),
    };
  }

  /**
   * Retrieves all student profiles based on provided filters.
   *
   * @param user - The user associated with the student profiles.
   * @param query - The query parameters for filtering student profiles.
   * @returns A list of student profiles.
   * @throws BadRequestException if fetching student profiles fails.
   */
  async getStudentProfiles({
    sort,
    limit,
    page,
    search,
    order,
    programme,
    institution_id,
    degree,
    level,
    username,
  }: StudentProfileQueryParamsDto) {
    // Order by the specified column
    const orderByDefault =
      order === 'asc'
        ? asc(student_profiles[sort as never])
        : desc(student_profiles[sort as never]);
    const institutionOrderBy =
      sort === 'institution'
        ? order === 'asc'
          ? asc(institutions.name)
          : desc(institutions.name)
        : orderByDefault;
    const levelOrderBy =
      sort === 'level'
        ? order === 'asc'
          ? asc(student_profiles.enrollment_date)
          : desc(student_profiles.enrollment_date)
        : institutionOrderBy;

    const where = [];
    const institutionWhere = [];
    // Add filters
    if (username) {
      where.push(ilike(student_profiles.username, `%${username}%`));
    }

    if (institution_id) {
      where.push(eq(student_profiles.institution_id, institution_id));
    }
    if (level) {
      const currentYear = new Date().getFullYear();
      where.push(
        sql`${currentYear} - ${student_profiles.enrollment_date} = ${level}`,
      );
    }

    if (programme) {
      where.push(eq(student_profiles.programme, programme));
    }

    if (degree) {
      where.push(eq(student_profiles.degree, degree));
    }

    if (search) {
      where.push(
        or(
          ilike(student_profiles.first_name, `%${search}%`),
          ilike(student_profiles.last_name, `%${search}%`),
          ilike(student_profiles.other_name, `%${search}%`),
        ),
      );
      institutionWhere.push(ilike(institutions.name, `%${search}%`));
    }

    const profiles = await this.drizzle.db
      .select({
        ...getTableColumns(student_profiles),
        user: {
          ...getTableColumns(users),
        },
        institution: {
          name: institutions.name,
        },
        country: {
          name: countries.name,
        },
      })
      .from(student_profiles)
      .leftJoin(users, eq(users.id, student_profiles.user_id))
      .innerJoin(
        institutions,
        eq(institutions.id, student_profiles.institution_id),
      )
      .leftJoin(countries, eq(countries.id, student_profiles.country_id))
      .where(and(...where))
      .orderBy(levelOrderBy)
      .limit(limit)
      .offset((page - 1) * limit);

    const total = await this.drizzle.db.$count(student_profiles, and(...where));

    return {
      data: profiles,
      total,
    };
  }

  /**
   * Retrieves all students based on provided filters.
   *
   * @param institutionId - Single or multiple institution IDs to filter by.
   * @param programme - Single or multiple programmes to filter by.
   * @param level - Single or multiple levels to filter by.
   * @param degree - Single or multiple degrees to filter by.
   * @returns A list of student profiles.
   * @throws BadRequestException if fetching students fails.
   */

  async getAllStudents(
    institutionId?: string | string[],
    programme?: string | string[],
    level?: number | number[],
    degree?: string | string[],
  ) {
    const filters: SQL[] = [];

    // Helper function to add filters
    const addFilter = <T>(
      value: T | T[] | undefined,
      column: any | SQL,
      options?: { useIlike?: boolean },
    ) => {
      if (value !== undefined) {
        if (Array.isArray(value) && value.length > 0) {
          filters.push(inArray(column, value));
        } else if (!Array.isArray(value)) {
          if (options?.useIlike && typeof value === 'string') {
            filters.push(ilike(column as any, `%${value}%`));
          } else {
            filters.push(eq(column, value));
          }
        }
      }
    };

    // Institution Filter
    addFilter(institutionId, student_profiles.institution_id);

    // Level Filter using current year - enrollment_date
    const currentYear = new Date().getFullYear();
    const enrollmentYearDiff = sql`${currentYear} - ${student_profiles.enrollment_date}`;
    addFilter(level, enrollmentYearDiff);

    // Programme Filter
    addFilter(programme, student_profiles.programme, { useIlike: true });

    // Degree Filter
    addFilter(degree, student_profiles.degree, { useIlike: true });

    // Build the query
    let query = this.drizzle.db
      .select({
        ...getTableColumns(student_profiles),
        user: {
          ...getTableColumns(users),
        },
        institution: {
          name: institutions.name,
          id: institutions.id,
        },
        country: {
          name: countries.name,
        },
      })
      .from(student_profiles)
      .leftJoin(
        users,
        and(eq(users.id, student_profiles.user_id), eq(users.deleted, false)),
      )
      .leftJoin(
        institutions,
        eq(institutions.id, student_profiles.institution_id),
      )
      .leftJoin(
        countries,
        eq(countries.id, student_profiles.country_id),
      ) as any;

    let countQuery = this.drizzle.db
      .select({ count: count() })
      .from(student_profiles);

    // Apply filters if any
    if (filters.length > 0) {
      query = query.where(and(...filters));
      countQuery = (countQuery as any).where(and(...filters));
    }

    const results = await query.execute();
    const total = await countQuery.execute();

    return { students: results, total: total[0]?.count ?? 0 };
  }

  /**
   * Removes a student profile from the database.
   *
   * @param studentProfileId - The ID of the student profile to remove.
   * @returns The deleted student profile.
   * @throws BadRequestException if the student profile is not found
   */
  async removeStudentProfile(studentProfileId: string) {
    const deletedProfile = await this.drizzle.db
      .delete(student_profiles)
      .where(eq(student_profiles.id, studentProfileId))
      .returning();
    if (!deletedProfile) {
      throw new BadRequestException('Student profile not found');
    }
    return deletedProfile;
  }

  /**
   * Retrieves the total number of students in the database.
   * @returns Total number of students in the database.
   */
  async getTotalStudent() {
    const students = await this.drizzle.db
      .select({ count: count() })
      .from(student_profiles)
      .where(
        and(eq(users.deleted, false), eq(users.state, user_states.ACTIVE)),
      );
    return students[0]?.count;
  }

  /**
   * Checks if all values in the profile object are not null or undefined.
   * @param profile The profile object to validate.
   * @returns True if all values are valid, false otherwise.
   */
  isProfileValid(profile: unknown | undefined | null): boolean {
    // Check if profile is null or undefined
    if (!profile) {
      return false;
    }

    // Ensure profile is an object
    if (typeof profile !== 'object' || profile === null) {
      return false;
    }

    // Check each value in the profile object
    return Object.values(profile).every(
      (value) => value !== null && value !== undefined,
    );
  }

  async getStudentProfileIdByUserId(user_id: string): Promise<string> {
    const [studentProfile] = await this.drizzle.db
      .select({ id: student_profiles.id })
      .from(student_profiles)
      .where(eq(student_profiles.user_id, user_id));

    return studentProfile?.id ?? '';
  }

  private slugifyName(name: string): string {
    return name
      .trim()
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '');
  }

  private generateUserHandle(firstName: string, lastName: string) {
    const base = this.slugifyName(firstName) + this.slugifyName(lastName);
    const randomNumber = Math.floor(100 + Math.random() * 9000);

    return `${base}${randomNumber}`;
  }

  private checkIfUsernameHasChanged(username_last_updated: string) {
    const currentTime = new Date();
    const timeDifference =
      currentTime.getTime() - new Date(username_last_updated).getTime();
    const timeDifferenceInHours = timeDifference / (1000 * 60 * 60);

    return timeDifferenceInHours > 3 * 30 * 24; // 3 months in hours
  }

  /**
   * Checks if a username is available (not taken).
   *
   * @param username - The username to check availability for.
   * @returns Object with usernameExist boolean indicating if username is taken.
   */
  async checkUsernameAvailability(username: string) {
    const existingProfile = await this.drizzle.db.query.student_profiles
      .findFirst({
        where: eq(student_profiles.username, username),
      })
      .execute();

    return {
      usernameExist: !!existingProfile,
    };
  }
}
