/* eslint-disable @typescript-eslint/no-unused-vars */
import { Test, TestingModule } from '@nestjs/testing';
import { StudentProfileController } from './student_profile.controller';
import { StudentProfileService } from './student_profile.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { RolesBuilder } from 'nest-access-control';
import { ROLES_BUILDER_TOKEN } from 'nest-access-control';
import { Reflector } from '@nestjs/core';
import { PointSystemService } from '@/point-system/point_system.service';
import { QuizService } from '@/mcq/quiz/quiz.service';
import {
  StudentProfileDto,
  StudentProfileCountQueryParam,
} from './student_profile.dto';
import { User } from '@/db/schema';
import { Logger } from '@nestjs/common';
import { SkillsService } from '@/skills/skills.service';

describe('StudentProfileController', () => {
  let controller: StudentProfileController;
  let service: StudentProfileService;

  const mockStudentProfileService = {
    addStudentProfile: jest.fn(),
    getStudentProfileById: jest.fn(),
    getStudentProfiles: jest.fn(),
    updateStudentProfile: jest.fn(),
    removeStudentProfile: jest.fn(),
    getAllStudents: jest.fn(),
    checkUsernameAvailability: jest.fn(),
  };

  const mockPointSystemService = {
    getStudentPoints: jest.fn(),
    createPointTransaction: jest.fn(),
    getPointTransactions: jest.fn(),
    getPointTransactionsByUserId: jest.fn(),
  };

  const mockQuizService = {
    getStudentQuizActivities: jest.fn(),
    getQuizzesByUserId: jest.fn(),
    getQuizById: jest.fn(),
  };

  const mockSkillsService = {
    getStudentSkills: jest.fn(),
  };

  const mockDrizzleService = {
    db: {
      $count: jest.fn().mockResolvedValue(10),
      query: jest.fn(),
      select: jest.fn(),
      insert: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    // Suppress console logs during tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});

    // Suppress NestJS Logger during tests
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});

    const module: TestingModule = await Test.createTestingModule({
      controllers: [StudentProfileController],
      providers: [
        {
          provide: StudentProfileService,
          useValue: mockStudentProfileService,
        },
        {
          provide: PointSystemService,
          useValue: mockPointSystemService,
        },
        {
          provide: QuizService,
          useValue: mockQuizService,
        },
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: ROLES_BUILDER_TOKEN,
          useValue: new RolesBuilder(),
        },
        {
          provide: SkillsService,
          useValue: mockSkillsService,
        },
      ],
    }).compile();

    controller = module.get<StudentProfileController>(StudentProfileController);
    service = module.get<StudentProfileService>(StudentProfileService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore console methods after each test
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should have the service defined', () => {
    expect(service).toBeDefined();
  });

  describe('countStudentProfiles', () => {
    it('should count student profiles', async () => {
      const mockResult = { count: 10, students: [] };
      const mockQueryParams: StudentProfileCountQueryParam = {
        institutionId:
          '123e4567-e89b-12d3-a456-426614174000,123e4567-e89b-12d3-a456-426614174001',
        programme: 'ICT,Engineering',
        level: '1,2',
        degree: 'Bachelors,Masters',
        search: '',
        sort: 'id',
        page: 1,
        limit: 10,
        order: 'asc',
        all: true,
      };

      mockStudentProfileService.getAllStudents.mockResolvedValue({
        total: 10,
        students: [],
      });

      const result = await controller.countStudentProfiles(mockQueryParams);

      expect(result).toEqual(mockResult);
      expect(service.getAllStudents).toHaveBeenCalled();
    });

    it('should handle errors when counting student profiles', async () => {
      const mockQueryParams: StudentProfileCountQueryParam = {
        institutionId:
          '123e4567-e89b-12d3-a456-426614174000,123e4567-e89b-12d3-a456-426614174001',
        programme: 'ICT,Engineering',
        level: '1,2',
        degree: 'Bachelors,Masters',
        search: '',
        sort: 'id',
        page: 1,
        limit: 10,
        order: 'asc',
        all: true,
      };

      mockStudentProfileService.getAllStudents.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(
        controller.countStudentProfiles(mockQueryParams),
      ).rejects.toThrow('Database error');
    });
  });

  describe('addStudentProfile', () => {
    it('should add a student profile', async () => {
      const mockProfileData: StudentProfileDto = {
        first_name: 'Test',
        last_name: 'Student',
        country_id: '123e4567-e89b-12d3-a456-426614174000',
        institution_id: '123e4567-e89b-12d3-a456-426614174001',
        programme: 'ICT',
        degree: 'Bachelors',
        enrollment_date: 2020,
        graduation_date: 2024,
      };

      const mockUser: User = {
        id: '123e4567-e89b-12d3-a456-426614174002',
        email: '<EMAIL>',
        role: 'student',
        state: 'active',
        profile_pic_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted: false,
        deleted_at: null,
      };

      const mockResult = {
        id: '123e4567-e89b-12d3-a456-426614174003',
        user_id: mockUser.id,
        first_name: 'Test',
        last_name: 'Student',
        other_name: null,
        username: null,
        country_id: '123e4567-e89b-12d3-a456-426614174000',
        date_of_birth: null,
        phone_number: null,
        institution_id: '123e4567-e89b-12d3-a456-426614174001',
        enrollment_date: 2020,
        graduation_date: 2024,
        degree: 'Bachelors',
        programme: 'ICT',
        github_profile: null,
        linkedin_profile: null,
        about: null,
        club_id: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted: false,
        deleted_at: null,
        email: '<EMAIL>',
        profile_pic_url: null,
        institution_name: 'Test Institution',
        country_name: 'Test Country',
        club_name: null,
        total_points: 100,
        leaderboard_rank: 1,
      };

      mockStudentProfileService.addStudentProfile.mockResolvedValue(mockResult);

      const result = await controller.addStudentProfile(
        mockProfileData,
        mockUser,
      );

      expect(result).toEqual(mockResult);
      expect(service.addStudentProfile).toHaveBeenCalledWith(
        mockProfileData,
        mockUser,
      );
    });

    it('should handle errors when adding a student profile', async () => {
      const mockProfileData: StudentProfileDto = {
        first_name: 'Test',
        last_name: 'Student',
        country_id: '123e4567-e89b-12d3-a456-426614174000',
        institution_id: '123e4567-e89b-12d3-a456-426614174001',
        programme: 'ICT',
        degree: 'Bachelors',
        enrollment_date: 2020,
        graduation_date: 2024,
      };

      const mockUser: User = {
        id: '123e4567-e89b-12d3-a456-426614174002',
        email: '<EMAIL>',
        role: 'student',
        state: 'active',
        profile_pic_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted: false,
        deleted_at: null,
      };

      mockStudentProfileService.addStudentProfile.mockRejectedValue(
        new Error('Database error'),
      );

      await expect(
        controller.addStudentProfile(mockProfileData, mockUser),
      ).rejects.toThrow('Database error');
    });
  });

  describe('checkUsernameAvailability', () => {
    it('should return usernameExist: false when username is available', async () => {
      const username = 'available_username';
      const mockResult = { usernameExist: false };

      mockStudentProfileService.checkUsernameAvailability.mockResolvedValue(
        mockResult,
      );

      const result = await controller.checkUsernameAvailability(username);

      expect(result).toEqual(mockResult);
      expect(service.checkUsernameAvailability).toHaveBeenCalledWith(username);
    });

    it('should return usernameExist: true when username is taken', async () => {
      const username = 'taken_username';
      const mockResult = { usernameExist: true };

      mockStudentProfileService.checkUsernameAvailability.mockResolvedValue(
        mockResult,
      );

      const result = await controller.checkUsernameAvailability(username);

      expect(result).toEqual(mockResult);
      expect(service.checkUsernameAvailability).toHaveBeenCalledWith(username);
    });

    it('should handle various username formats correctly', async () => {
      const testCases = [
        { username: 'user123', expected: { usernameExist: false } },
        { username: 'john_doe', expected: { usernameExist: false } },
        { username: 'test_user_456', expected: { usernameExist: true } },
        { username: 'simple', expected: { usernameExist: false } },
      ];

      for (const testCase of testCases) {
        mockStudentProfileService.checkUsernameAvailability.mockResolvedValue(
          testCase.expected,
        );

        const result = await controller.checkUsernameAvailability(
          testCase.username,
        );

        expect(result).toEqual(testCase.expected);
        expect(service.checkUsernameAvailability).toHaveBeenCalledWith(
          testCase.username,
        );
      }
    });

    it('should handle database errors gracefully', async () => {
      const username = 'test_username';
      const error = new Error('Database connection failed');

      mockStudentProfileService.checkUsernameAvailability.mockRejectedValue(
        error,
      );

      await expect(
        controller.checkUsernameAvailability(username),
      ).rejects.toThrow('Database connection failed');

      expect(service.checkUsernameAvailability).toHaveBeenCalledWith(username);
    });

    it('should handle service errors and rethrow them', async () => {
      const username = 'error_username';
      const error = new Error('Service unavailable');

      mockStudentProfileService.checkUsernameAvailability.mockRejectedValue(
        error,
      );

      await expect(
        controller.checkUsernameAvailability(username),
      ).rejects.toThrow('Service unavailable');
    });

    it('should call the service with the exact username parameter', async () => {
      const username = 'exact_match_test';
      const mockResult = { usernameExist: false };

      mockStudentProfileService.checkUsernameAvailability.mockResolvedValue(
        mockResult,
      );

      await controller.checkUsernameAvailability(username);

      expect(service.checkUsernameAvailability).toHaveBeenCalledTimes(1);
      expect(service.checkUsernameAvailability).toHaveBeenCalledWith(
        'exact_match_test',
      );
    });
  });
});
